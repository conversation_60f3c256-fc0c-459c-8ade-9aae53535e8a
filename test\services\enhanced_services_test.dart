import 'package:flutter_test/flutter_test.dart';
import 'package:mockito/mockito.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:firebase_storage/firebase_storage.dart';

import '../../lib/services/enhanced_document_service.dart';
import '../../lib/services/enhanced_firebase_storage_service.dart';
import '../../lib/services/enhanced_auth_service.dart';
import '../../lib/models/document_model.dart';
import '../../lib/models/user_model.dart';
import '../helpers/mock_services.dart';

void main() {
  group('Enhanced Services Tests', () {
    late MockFirebaseService mockFirebaseService;
    late MockAuthService mockAuthService;

    setUp(() {
      mockFirebaseService = MockFirebaseService();
      mockAuthService = MockAuthService();
    });

    group('EnhancedDocumentService', () {
      test('should allow unlimited queries for admin users', () async {
        // Arrange
        final service = EnhancedDocumentService.instance;
        
        // Mock admin user
        final adminUser = UserModel(
          id: 'admin123',
          fullName: 'Admin User',
          email: '<EMAIL>',
          role: 'admin',
          status: 'active',
          permissions: UserPermissions.admin(),
        );

        when(mockAuthService.currentUser).thenReturn(adminUser);

        // Act & Assert
        expect(service.canPerformUnlimitedQueries, isTrue);
      });

      test('should deny unlimited queries for regular users', () async {
        // Arrange
        final service = EnhancedDocumentService.instance;
        
        // Mock regular user
        final regularUser = UserModel(
          id: 'user123',
          fullName: 'Regular User',
          email: '<EMAIL>',
          role: 'user',
          status: 'active',
          permissions: UserPermissions.user(),
        );

        when(mockAuthService.currentUser).thenReturn(regularUser);

        // Act & Assert
        expect(service.canPerformUnlimitedQueries, isFalse);
      });

      test('should return empty list when user is null', () async {
        // Arrange
        final service = EnhancedDocumentService.instance;
        when(mockAuthService.currentUser).thenReturn(null);

        // Act
        final result = await service.getAllDocumentsUnlimited();

        // Assert
        expect(result, isEmpty);
      });

      test('should get total document count for admin', () async {
        // Arrange
        final service = EnhancedDocumentService.instance;
        
        final adminUser = UserModel(
          id: 'admin123',
          fullName: 'Admin User',
          email: '<EMAIL>',
          role: 'admin',
          status: 'active',
          permissions: UserPermissions.admin(),
        );

        when(mockAuthService.currentUser).thenReturn(adminUser);

        // Act
        final count = await service.getTotalDocumentCount();

        // Assert
        expect(count, isA<int>());
        expect(count, greaterThanOrEqualTo(0));
      });
    });

    group('EnhancedFirebaseStorageService', () {
      test('should cache download URLs', () async {
        // Arrange
        final service = EnhancedFirebaseStorageService.instance;
        
        // Act
        service.clearUrlCache(); // Start with clean cache

        // Assert
        expect(service.canAccessUnlimitedStorage, isA<bool>());
      });

      test('should get storage statistics for admin users', () async {
        // Arrange
        final service = EnhancedFirebaseStorageService.instance;

        // Act
        final stats = await service.getStorageStatistics();

        // Assert
        expect(stats, isA<Map<String, dynamic>>());
      });

      test('should refresh download URL', () async {
        // Arrange
        final service = EnhancedFirebaseStorageService.instance;
        const testPath = 'documents/test_file.pdf';

        // Act
        final url = await service.refreshDownloadUrl(testPath);

        // Assert - URL can be null if file doesn't exist, which is expected in test
        expect(url, anyOf(isNull, isA<String>()));
      });
    });

    group('EnhancedAuthService', () {
      test('should check admin privileges correctly', () {
        // Arrange
        final service = EnhancedAuthService.instance;
        
        final adminUser = UserModel(
          id: 'admin123',
          fullName: 'Admin User',
          email: '<EMAIL>',
          role: 'admin',
          status: 'active',
          permissions: UserPermissions.admin(),
        );

        when(mockAuthService.currentUser).thenReturn(adminUser);

        // Act & Assert
        expect(service.isCurrentUserAdmin, isTrue);
      });

      test('should validate permissions correctly', () {
        // Arrange
        final service = EnhancedAuthService.instance;
        
        final validPermissions = UserPermissions(
          documents: ['view', 'upload'],
          categories: [],
          system: ['analytics'],
        );

        final invalidPermissions = UserPermissions(
          documents: ['invalid_permission'],
          categories: [],
          system: [],
        );

        // Act & Assert
        expect(service.validatePermissions(validPermissions), isTrue);
        expect(service.validatePermissions(invalidPermissions), isFalse);
      });

      test('should get default permissions for roles', () {
        // Arrange
        final service = EnhancedAuthService.instance;

        // Act
        final adminPerms = service.getDefaultPermissionsForRole('admin');
        final userPerms = service.getDefaultPermissionsForRole('user');

        // Assert
        expect(adminPerms.documents, contains('delete'));
        expect(adminPerms.system, contains('user_management'));
        expect(userPerms.documents, contains('view'));
        expect(userPerms.documents, isNot(contains('delete')));
      });

      test('should get user role display names', () {
        // Arrange
        final service = EnhancedAuthService.instance;

        // Act & Assert
        expect(service.getUserRoleDisplayName('admin'), equals('Administrator'));
        expect(service.getUserRoleDisplayName('user'), equals('User'));
        expect(service.getUserRoleDisplayName('unknown'), equals('Unknown'));
      });

      test('should check document permissions', () async {
        // Arrange
        final service = EnhancedAuthService.instance;
        
        final userWithUpload = UserModel(
          id: 'user123',
          fullName: 'User',
          email: '<EMAIL>',
          role: 'user',
          status: 'active',
          permissions: UserPermissions(
            documents: ['view', 'upload'],
            categories: [],
            system: [],
          ),
        );

        when(mockAuthService.currentUser).thenReturn(userWithUpload);

        // Act & Assert
        expect(await service.hasDocumentPermission('view'), isTrue);
        expect(await service.hasDocumentPermission('upload'), isTrue);
        expect(await service.hasDocumentPermission('delete'), isFalse);
      });

      test('should clear permission cache', () {
        // Arrange
        final service = EnhancedAuthService.instance;

        // Act
        service.clearAllPermissionCache();
        service.clearUserPermissionCache('test_user');

        // Assert - No exception should be thrown
        expect(true, isTrue);
      });
    });

    group('Integration Tests', () {
      test('should work together for admin workflow', () async {
        // Arrange
        final documentService = EnhancedDocumentService.instance;
        final storageService = EnhancedFirebaseStorageService.instance;
        final authService = EnhancedAuthService.instance;

        final adminUser = UserModel(
          id: 'admin123',
          fullName: 'Admin User',
          email: '<EMAIL>',
          role: 'admin',
          status: 'active',
          permissions: UserPermissions.admin(),
        );

        when(mockAuthService.currentUser).thenReturn(adminUser);

        // Act & Assert
        expect(documentService.canPerformUnlimitedQueries, isTrue);
        expect(storageService.canAccessUnlimitedStorage, isTrue);
        expect(authService.isCurrentUserAdmin, isTrue);
      });

      test('should work together for user workflow', () async {
        // Arrange
        final documentService = EnhancedDocumentService.instance;
        final storageService = EnhancedFirebaseStorageService.instance;
        final authService = EnhancedAuthService.instance;

        final regularUser = UserModel(
          id: 'user123',
          fullName: 'Regular User',
          email: '<EMAIL>',
          role: 'user',
          status: 'active',
          permissions: UserPermissions.user(),
        );

        when(mockAuthService.currentUser).thenReturn(regularUser);

        // Act & Assert
        expect(documentService.canPerformUnlimitedQueries, isFalse);
        expect(authService.isCurrentUserAdmin, isFalse);
      });
    });
  });
}
